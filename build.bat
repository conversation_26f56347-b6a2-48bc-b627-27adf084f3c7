@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

rem Augment清理工具构建脚本 - 批处理版本

echo 🧹 Augment清理工具 - 跨平台构建
echo ==================================

rem 检查Go环境
where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Go编译环境
    echo 请先安装Go: https://golang.org/dl/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('go version') do set GO_VERSION=%%i
echo ✅ 检测到Go编译环境: !GO_VERSION!

rem 创建构建目录
if not exist "dist" mkdir dist

echo 📦 开始编译清理工具...

echo.
echo 🏗️ AMD64架构 (x86_64)
rem 构建Windows AMD64版本
echo 构建 windows/amd64...
set GOOS=windows
set GOARCH=amd64
go build -o "dist\augment-cleaner-windows-amd64.exe" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-windows-amd64.exe
) else (
    echo ❌ 构建失败: augment-cleaner-windows-amd64.exe
)

rem 构建Linux AMD64版本
echo 构建 linux/amd64...
set GOOS=linux
set GOARCH=amd64
go build -o "dist\augment-cleaner-linux-amd64" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-linux-amd64
) else (
    echo ❌ 构建失败: augment-cleaner-linux-amd64
)

rem 构建macOS AMD64版本
echo 构建 darwin/amd64...
set GOOS=darwin
set GOARCH=amd64
go build -o "dist\augment-cleaner-darwin-amd64" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-darwin-amd64
) else (
    echo ❌ 构建失败: augment-cleaner-darwin-amd64
)

echo.
echo 🏗️ ARM64架构 (现代ARM设备)
rem 构建Windows ARM64版本
echo 构建 windows/arm64...
set GOOS=windows
set GOARCH=arm64
go build -o "dist\augment-cleaner-windows-arm64.exe" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-windows-arm64.exe
) else (
    echo ❌ 构建失败: augment-cleaner-windows-arm64.exe
)

rem 构建Linux ARM64版本
echo 构建 linux/arm64...
set GOOS=linux
set GOARCH=arm64
go build -o "dist\augment-cleaner-linux-arm64" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-linux-arm64
) else (
    echo ❌ 构建失败: augment-cleaner-linux-arm64
)

rem 构建macOS ARM64版本
echo 构建 darwin/arm64...
set GOOS=darwin
set GOARCH=arm64
go build -o "dist\augment-cleaner-darwin-arm64" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-darwin-arm64
) else (
    echo ❌ 构建失败: augment-cleaner-darwin-arm64
)

echo.
echo 🏗️ ARM32架构 (嵌入式设备)
rem 构建Linux ARM32版本
echo 构建 linux/arm...
set GOOS=linux
set GOARCH=arm
go build -o "dist\augment-cleaner-linux-arm" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-linux-arm
) else (
    echo ❌ 构建失败: augment-cleaner-linux-arm
)

echo.
echo 📁 构建结果:
dir /B dist\

echo.
echo 🎉 构建完成！
echo.
echo 使用方法:
echo.
echo AMD64架构 (x86_64):
echo   Windows: dist\augment-cleaner-windows-amd64.exe
echo   Linux:   dist\augment-cleaner-linux-amd64
echo   macOS:   dist\augment-cleaner-darwin-amd64
echo.
echo ARM64架构 (Apple Silicon, ARM服务器):
echo   Windows: dist\augment-cleaner-windows-arm64.exe
echo   Linux:   dist\augment-cleaner-linux-arm64
echo   macOS:   dist\augment-cleaner-darwin-arm64
echo.
echo ARM32架构 (树莓派等嵌入式设备):
echo   Linux:   dist\augment-cleaner-linux-arm
echo.
echo 提示: 可以直接双击exe文件运行Windows版本
echo.
pause 