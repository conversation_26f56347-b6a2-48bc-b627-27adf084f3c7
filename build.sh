#!/bin/bash

# Augment清理工具构建脚本

echo "🧹 Augment清理工具 - 跨平台构建"
echo "=================================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 未找到Go编译环境"
    exit 1
fi

echo "✅ 检测到Go编译环境: $(go version)"

# 创建构建目录
mkdir -p dist

echo "📦 开始编译清理工具..."

# 构建目标平台
platforms=(
    "windows/amd64"
    "linux/amd64"
    "darwin/amd64"
)

for platform in "${platforms[@]}"; do
    IFS='/' read -r os arch <<< "$platform"
    
    echo "构建 $os/$arch..."
    
    output_name="augment-cleaner-$os-$arch"
    if [ "$os" = "windows" ]; then
        output_name="${output_name}.exe"
    fi
    
    env GOOS=$os GOARCH=$arch go build -o "dist/$output_name" augment_cleaner.go
    
    if [ $? -eq 0 ]; then
        echo "✅ $output_name"
    else
        echo "❌ 构建失败: $output_name"
    fi
done

echo ""
echo "📁 构建结果:"
ls -la dist/

echo ""
echo "🎉 构建完成！"
echo ""
echo "使用方法:"
echo "Windows: dist/augment-cleaner-windows-amd64.exe"
echo "Linux:   dist/augment-cleaner-linux-amd64"
echo "macOS:   dist/augment-cleaner-darwin-amd64"

